import * as React from "react"
import { Filter, RotateCcw, Search } from "lucide-react"
import { DateRange } from "react-day-picker"

import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { DateRangePicker } from "@/components/ui/date-range-picker"
import { CompanySelector, Company } from "@/components/ui/company-selector"
import { QuarterSelector, Quarter, getCompaniesForQuarter } from "@/components/ui/quarter-selector"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

export interface ReportsFilters {
  dateRange?: DateRange
  selectedCompanies: Company[]
  selectedQuarter?: Quarter
  searchTerm: string
}

interface ReportsFiltersProps {
  filters: ReportsFilters
  onFiltersChange: (filters: ReportsFilters) => void
  companies: Company[]
  className?: string
  variant?: "default" | "glass"
}

export function ReportsFilters({
  filters,
  onFiltersChange,
  companies,
  className,
  variant = "glass",
}: ReportsFiltersProps) {
  const [isExpanded, setIsExpanded] = React.useState(false)

  const handleDateRangeChange = (dateRange: DateRange | undefined) => {
    onFiltersChange({
      ...filters,
      dateRange,
      selectedQuarter: undefined, // Clear quarter when date range is selected
    })
  }

  const handleCompanySelectionChange = (selectedCompanies: Company[]) => {
    onFiltersChange({
      ...filters,
      selectedCompanies,
    })
  }

  const handleQuarterChange = (selectedQuarter: Quarter | undefined) => {
    if (selectedQuarter) {
      // Auto-select companies added during this quarter
      const quarterCompanies = getCompaniesForQuarter(
        companies.map(c => ({ ...c, createdAt: new Date() })), // Mock created dates
        selectedQuarter
      )
      
      onFiltersChange({
        ...filters,
        selectedQuarter,
        selectedCompanies: quarterCompanies,
        dateRange: {
          from: selectedQuarter.startDate,
          to: selectedQuarter.endDate,
        },
      })
    } else {
      onFiltersChange({
        ...filters,
        selectedQuarter: undefined,
      })
    }
  }

  const handleSearchChange = (searchTerm: string) => {
    onFiltersChange({
      ...filters,
      searchTerm,
    })
  }

  const handleReset = () => {
    onFiltersChange({
      dateRange: undefined,
      selectedCompanies: [],
      selectedQuarter: undefined,
      searchTerm: "",
    })
  }

  const hasActiveFilters = 
    filters.dateRange || 
    filters.selectedCompanies.length > 0 || 
    filters.selectedQuarter || 
    filters.searchTerm

  return (
    <Card 
      variant={variant} 
      className={cn(
        "p-4 space-y-4",
        variant === "glass" && "shadow-xl",
        className
      )}
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Filter className="h-5 w-5 text-primary" />
          <h3 className="text-subtitle-large font-semibold">Filters</h3>
          {hasActiveFilters && (
            <Badge variant="glass" className="text-xs">
              {[
                filters.dateRange && "Date Range",
                filters.selectedCompanies.length > 0 && `${filters.selectedCompanies.length} Companies`,
                filters.selectedQuarter && "Quarter",
                filters.searchTerm && "Search"
              ].filter(Boolean).length} active
            </Badge>
          )}
        </div>
        <div className="flex items-center space-x-2">
          {hasActiveFilters && (
            <Button
              variant="glass"
              size="sm"
              onClick={handleReset}
              className="text-xs"
            >
              <RotateCcw className="h-3 w-3 mr-1" />
              Reset
            </Button>
          )}
          <Button
            variant="glass"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="md:hidden"
          >
            {isExpanded ? "Collapse" : "Expand"}
          </Button>
        </div>
      </div>

      {/* Filters Grid */}
      <div className={cn(
        "grid gap-4",
        isExpanded ? "grid-cols-1" : "hidden md:grid md:grid-cols-2 lg:grid-cols-4"
      )}>
        {/* Search */}
        <div className="space-y-2">
          <label className="text-subtitle font-medium">Search</label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              variant={variant}
              placeholder="Search listings..."
              value={filters.searchTerm}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-9"
            />
          </div>
        </div>

        {/* Date Range */}
        <div className="space-y-2">
          <label className="text-subtitle font-medium">Date Range</label>
          <DateRangePicker
            variant={variant}
            date={filters.dateRange}
            onDateChange={handleDateRangeChange}
            placeholder="Select date range"
          />
        </div>

        {/* Quarter Selector */}
        <div className="space-y-2">
          <label className="text-subtitle font-medium">Quarter</label>
          <QuarterSelector
            variant={variant}
            selectedQuarter={filters.selectedQuarter}
            onQuarterChange={handleQuarterChange}
            placeholder="Select quarter"
          />
        </div>

        {/* Company Selector */}
        <div className="space-y-2">
          <label className="text-subtitle font-medium">Companies</label>
          <CompanySelector
            variant={variant}
            companies={companies}
            selectedCompanies={filters.selectedCompanies}
            onSelectionChange={handleCompanySelectionChange}
            placeholder="Select companies"
          />
        </div>
      </div>

      {/* Active Filters Summary */}
      {hasActiveFilters && (
        <div className="pt-2 border-t border-white/20">
          <div className="flex flex-wrap gap-2">
            {filters.dateRange && (
              <Badge variant="glass" className="text-xs">
                {filters.dateRange.from?.toLocaleDateString()} - {filters.dateRange.to?.toLocaleDateString()}
              </Badge>
            )}
            {filters.selectedQuarter && (
              <Badge variant="glass" className="text-xs">
                {filters.selectedQuarter.label}
              </Badge>
            )}
            {filters.selectedCompanies.map((company) => (
              <Badge key={company.id} variant="glass" className="text-xs">
                {company.name}
              </Badge>
            ))}
            {filters.searchTerm && (
              <Badge variant="glass" className="text-xs">
                Search: "{filters.searchTerm}"
              </Badge>
            )}
          </div>
        </div>
      )}
    </Card>
  )
}
