import React, { useState, useMemo } from "react";
import {
  Bar<PERSON><PERSON>3,
  TrendingUp,
  TrendingDown,
  DollarSign,
  FileText,
  Calendar,
  Download,
  Building,
  Users,
  Package,
  Eye,
  MessageSquare,
  Clock,
} from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  ReportsFilters,
  ReportsFilters as FiltersType,
} from "@/components/reports/ReportsFilters";
import { Company } from "@/components/ui/company-selector";
import { getStatusBadgeVariant, getStatusLabel } from "@/lib/status-utils";

interface ReportsProps {
  className?: string;
}

export const ReportsComponent: React.FC<ReportsProps> = ({ className }) => {
  const [activeTab, setActiveTab] = useState("overview");
  const [filters, setFilters] = useState<FiltersType>({
    selectedCompanies: [],
    searchTerm: "",
  });

  // Mock data - in a real app, this would come from API
  const mockCompanies: Company[] = [
    { id: "1", name: "Downtown Restaurant", industry: "Restaurant" },
    { id: "2", name: "Auto Repair Shop", industry: "Automotive" },
    { id: "3", name: "Retail Store", industry: "Retail" },
    { id: "4", name: "Tech Startup", industry: "Technology" },
    { id: "5", name: "Coffee Shop", industry: "Food & Beverage" },
    { id: "6", name: "Fitness Center", industry: "Health & Fitness" },
  ];

  const reportData = {
    overview: {
      totalListings: 45,
      activeListings: 23,
      underContract: 7,
      closedDeals: 15,
      totalCommissions: 245000,
      avgDaysOnMarket: 67,
      conversionRate: 33.3,
    },
    listings: [
      {
        id: "1",
        businessName: "Downtown Restaurant",
        industry: "Restaurant",
        askingPrice: 450000,
        status: "Active",
        daysListed: 45,
        views: 234,
        inquiries: 12,
        companyId: "1",
      },
      {
        id: "2",
        businessName: "Auto Repair Shop",
        industry: "Automotive",
        askingPrice: 275000,
        status: "Under Contract",
        daysListed: 23,
        views: 156,
        inquiries: 8,
        companyId: "2",
      },
      {
        id: "3",
        businessName: "Retail Store",
        industry: "Retail",
        askingPrice: 125000,
        status: "Sold",
        daysListed: 89,
        views: 89,
        inquiries: 5,
        companyId: "3",
      },
      {
        id: "4",
        businessName: "Tech Startup",
        industry: "Technology",
        askingPrice: 850000,
        status: "Active",
        daysListed: 12,
        views: 456,
        inquiries: 23,
        companyId: "4",
      },
      {
        id: "5",
        businessName: "Coffee Shop",
        industry: "Food & Beverage",
        askingPrice: 180000,
        status: "Draft",
        daysListed: 5,
        views: 67,
        inquiries: 3,
        companyId: "5",
      },
    ],
    performance: {
      monthlyData: [
        { month: "Jan", listings: 8, closed: 2, revenue: 45000 },
        { month: "Feb", listings: 12, closed: 3, revenue: 67000 },
        { month: "Mar", listings: 15, closed: 4, revenue: 89000 },
        { month: "Apr", listings: 18, closed: 6, revenue: 123000 },
      ],
    },
  };

  // Filter listings based on current filters
  const filteredListings = useMemo(() => {
    return reportData.listings.filter((listing) => {
      // Company filter
      if (filters.selectedCompanies.length > 0) {
        const companyIds = filters.selectedCompanies.map((c) => c.id);
        if (!companyIds.includes(listing.companyId)) {
          return false;
        }
      }

      // Search filter
      if (filters.searchTerm) {
        const searchLower = filters.searchTerm.toLowerCase();
        return (
          listing.businessName.toLowerCase().includes(searchLower) ||
          listing.industry.toLowerCase().includes(searchLower) ||
          listing.status.toLowerCase().includes(searchLower)
        );
      }

      return true;
    });
  }, [reportData.listings, filters]);

  return (
    <div className={className}>
      <div className="min-h-screen texture-bg">
        <div className="space-y-6 p-4 sm:p-6 lg:p-8">
          {/* Glassmorphic Header */}
          <Card variant="glass" className="shadow-xl">
            <CardContent className="p-6">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <h1 className="text-title font-bold tracking-tight">
                    Reports & Analytics
                  </h1>
                  <p className="text-subtitle text-muted-foreground">
                    Analyze your business performance and track key metrics with
                    advanced filtering.
                  </p>
                </div>
                <div className="mt-4 flex gap-3 sm:mt-0">
                  <Button variant="glass-highlight">
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Advanced Filters */}
          <ReportsFilters
            filters={filters}
            onFiltersChange={setFilters}
            companies={mockCompanies}
            variant="glass"
          />

          {/* Glassmorphic Reports Tabs */}
          <Card variant="glass" className="shadow-xl">
            <CardContent className="p-6">
              <Tabs
                value={activeTab}
                onValueChange={setActiveTab}
                className="space-y-6"
              >
                <TabsList className="glass-card-subtle grid w-full grid-cols-3 p-1">
                  <TabsTrigger
                    value="overview"
                    className="flex items-center space-x-2 data-[state=active]:glass-card data-[state=active]:shadow-lg"
                  >
                    <BarChart3 className="h-4 w-4" />
                    <span className="hidden sm:inline">Overview</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="listings"
                    className="flex items-center space-x-2 data-[state=active]:glass-card data-[state=active]:shadow-lg"
                  >
                    <Building className="h-4 w-4" />
                    <span className="hidden sm:inline">Listings</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="performance"
                    className="flex items-center space-x-2 data-[state=active]:glass-card data-[state=active]:shadow-lg"
                  >
                    <TrendingUp className="h-4 w-4" />
                    <span className="hidden sm:inline">Performance</span>
                  </TabsTrigger>
                </TabsList>

                {/* Overview Tab */}
                <TabsContent value="overview" className="space-y-6">
                  {/* Glassmorphic KPI Cards */}
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card
                      variant="glass"
                      className="shadow-xl shadow-blue-500/10"
                    >
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div className="space-y-2">
                            <p className="text-subtitle font-medium text-muted-foreground">
                              Total Listings
                            </p>
                            <p className="text-2xl md:text-3xl font-bold text-foreground">
                              {reportData.overview.totalListings}
                            </p>
                          </div>
                          <div className="w-12 h-12 glass-card rounded-xl flex items-center justify-center">
                            <Building className="w-6 h-6 text-blue-500" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card
                      variant="glass"
                      className="shadow-xl shadow-emerald-500/10"
                    >
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div className="space-y-2">
                            <p className="text-subtitle font-medium text-muted-foreground">
                              Active Listings
                            </p>
                            <p className="text-2xl md:text-3xl font-bold text-foreground">
                              {reportData.overview.activeListings}
                            </p>
                            <div className="flex items-center space-x-1 text-sm">
                              <TrendingUp className="w-4 h-4 text-emerald-500" />
                              <span className="text-emerald-600 font-medium">
                                +12%
                              </span>
                            </div>
                          </div>
                          <div className="w-12 h-12 glass-card rounded-xl flex items-center justify-center">
                            <Package className="w-6 h-6 text-emerald-500" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card
                      variant="glass"
                      className="shadow-xl shadow-amber-500/10"
                    >
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div className="space-y-2">
                            <p className="text-subtitle font-medium text-muted-foreground">
                              Under Contract
                            </p>
                            <p className="text-2xl md:text-3xl font-bold text-foreground">
                              {reportData.overview.underContract}
                            </p>
                          </div>
                          <div className="w-12 h-12 glass-card rounded-xl flex items-center justify-center">
                            <FileText className="w-6 h-6 text-amber-500" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card
                      variant="glass"
                      className="shadow-xl shadow-purple-500/10"
                    >
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div className="space-y-2">
                            <p className="text-subtitle font-medium text-muted-foreground">
                              Closed Deals
                            </p>
                            <p className="text-2xl md:text-3xl font-bold text-foreground">
                              {reportData.overview.closedDeals}
                            </p>
                            <div className="flex items-center space-x-1 text-sm">
                              <TrendingUp className="w-4 h-4 text-purple-500" />
                              <span className="text-purple-600 font-medium">
                                +8%
                              </span>
                            </div>
                          </div>
                          <div className="w-12 h-12 glass-card rounded-xl flex items-center justify-center">
                            <TrendingUp className="w-6 h-6 text-purple-500" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Additional Glassmorphic Metrics */}
                  <div className="grid gap-4 md:grid-cols-3">
                    <Card
                      variant="glass"
                      className="shadow-xl shadow-green-500/10"
                    >
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-subtitle font-medium">
                          Total Commissions
                        </CardTitle>
                        <DollarSign className="h-4 w-4 text-green-500" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-foreground">
                          $
                          {reportData.overview.totalCommissions.toLocaleString()}
                        </div>
                        <p className="text-xs text-muted-foreground">
                          +15% from last period
                        </p>
                      </CardContent>
                    </Card>

                    <Card
                      variant="glass"
                      className="shadow-xl shadow-orange-500/10"
                    >
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-subtitle font-medium">
                          Avg Days on Market
                        </CardTitle>
                        <Calendar className="h-4 w-4 text-orange-500" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-foreground">
                          {reportData.overview.avgDaysOnMarket}
                        </div>
                        <p className="text-xs text-muted-foreground">
                          -5 days from last period
                        </p>
                      </CardContent>
                    </Card>

                    <Card
                      variant="glass"
                      className="shadow-xl shadow-indigo-500/10"
                    >
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-subtitle font-medium">
                          Conversion Rate
                        </CardTitle>
                        <TrendingUp className="h-4 w-4 text-indigo-500" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-foreground">
                          {reportData.overview.conversionRate}%
                        </div>
                        <p className="text-xs text-muted-foreground">
                          +2.1% from last period
                        </p>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                {/* Listings Tab with Filtering */}
                <TabsContent value="listings" className="space-y-6">
                  <Card variant="glass" className="shadow-xl">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div>
                          <CardTitle className="text-subtitle-large">
                            Filtered Listing Performance
                          </CardTitle>
                          <CardDescription className="text-subtitle">
                            Detailed performance metrics for your filtered
                            listings ({filteredListings.length} results).
                          </CardDescription>
                        </div>
                        <Badge variant="glass" className="text-xs">
                          {filteredListings.length} of{" "}
                          {reportData.listings.length} listings
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {filteredListings.length === 0 ? (
                          <div className="text-center py-12">
                            <div className="w-12 h-12 glass-card rounded-xl flex items-center justify-center mx-auto mb-4">
                              <Building className="w-6 h-6 text-muted-foreground" />
                            </div>
                            <h3 className="text-subtitle-large font-semibold text-foreground mb-2">
                              No listings found
                            </h3>
                            <p className="text-subtitle text-muted-foreground">
                              Try adjusting your filters to see more results.
                            </p>
                          </div>
                        ) : (
                          filteredListings.map((listing) => (
                            <Card
                              key={listing.id}
                              variant="glass-subtle"
                              className="p-4 hover:shadow-lg transition-all duration-300"
                            >
                              <div className="flex items-center justify-between">
                                <div className="space-y-2 flex-1">
                                  <div className="flex items-center space-x-3">
                                    <div className="w-10 h-10 glass-card rounded-lg flex items-center justify-center">
                                      <Building className="w-5 h-5 text-primary" />
                                    </div>
                                    <div className="flex-1">
                                      <div className="flex items-center space-x-2">
                                        <h3 className="font-semibold text-foreground">
                                          {listing.businessName}
                                        </h3>
                                        <Badge
                                          variant={getStatusBadgeVariant(
                                            listing.status
                                          )}
                                        >
                                          {getStatusLabel(listing.status)}
                                        </Badge>
                                      </div>
                                      <p className="text-sm text-muted-foreground">
                                        {listing.industry}
                                      </p>
                                    </div>
                                  </div>
                                  <div className="flex items-center space-x-6 text-sm text-muted-foreground ml-13">
                                    <div className="flex items-center space-x-1">
                                      <Clock className="w-4 h-4" />
                                      <span>
                                        {listing.daysListed} days listed
                                      </span>
                                    </div>
                                    <div className="flex items-center space-x-1">
                                      <Eye className="w-4 h-4" />
                                      <span>{listing.views} views</span>
                                    </div>
                                    <div className="flex items-center space-x-1">
                                      <MessageSquare className="w-4 h-4" />
                                      <span>{listing.inquiries} inquiries</span>
                                    </div>
                                  </div>
                                </div>
                                <div className="text-right">
                                  <div className="text-lg font-semibold text-foreground">
                                    ${listing.askingPrice.toLocaleString()}
                                  </div>
                                  <p className="text-xs text-muted-foreground">
                                    Asking Price
                                  </p>
                                </div>
                              </div>
                            </Card>
                          ))
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Performance Tab */}
                <TabsContent value="performance" className="space-y-6">
                  <Card variant="glass" className="shadow-xl">
                    <CardHeader>
                      <CardTitle className="text-subtitle-large">
                        Monthly Performance
                      </CardTitle>
                      <CardDescription className="text-subtitle">
                        Track your monthly listing and revenue performance
                        trends.
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {reportData.performance.monthlyData.map(
                          (month, index) => (
                            <Card
                              key={month.month}
                              variant="glass-subtle"
                              className="p-4 hover:shadow-lg transition-all duration-300"
                            >
                              <div className="flex items-center justify-between">
                                <div className="space-y-2">
                                  <div className="flex items-center space-x-3">
                                    <div className="w-10 h-10 glass-card rounded-lg flex items-center justify-center">
                                      <Calendar className="w-5 h-5 text-primary" />
                                    </div>
                                    <div>
                                      <h3 className="font-semibold text-foreground">
                                        {month.month} 2024
                                      </h3>
                                      <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                                        <span>{month.listings} listings</span>
                                        <span>{month.closed} closed</span>
                                        <span>
                                          {month.closed > 0
                                            ? Math.round(
                                                (month.closed /
                                                  month.listings) *
                                                  100
                                              )
                                            : 0}
                                          % conversion
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div className="text-right">
                                  <div className="text-lg font-semibold text-foreground">
                                    ${month.revenue.toLocaleString()}
                                  </div>
                                  <p className="text-sm text-muted-foreground">
                                    Revenue
                                  </p>
                                  {index > 0 && (
                                    <div className="flex items-center justify-end space-x-1 text-xs mt-1">
                                      {month.revenue >
                                      reportData.performance.monthlyData[
                                        index - 1
                                      ].revenue ? (
                                        <>
                                          <TrendingUp className="w-3 h-3 text-emerald-500" />
                                          <span className="text-emerald-600">
                                            +
                                            {Math.round(
                                              ((month.revenue -
                                                reportData.performance
                                                  .monthlyData[index - 1]
                                                  .revenue) /
                                                reportData.performance
                                                  .monthlyData[index - 1]
                                                  .revenue) *
                                                100
                                            )}
                                            %
                                          </span>
                                        </>
                                      ) : (
                                        <>
                                          <TrendingDown className="w-3 h-3 text-red-500" />
                                          <span className="text-red-600">
                                            {Math.round(
                                              ((month.revenue -
                                                reportData.performance
                                                  .monthlyData[index - 1]
                                                  .revenue) /
                                                reportData.performance
                                                  .monthlyData[index - 1]
                                                  .revenue) *
                                                100
                                            )}
                                            %
                                          </span>
                                        </>
                                      )}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </Card>
                          )
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Performance Summary */}
                  <Card variant="glass" className="shadow-xl">
                    <CardHeader>
                      <CardTitle className="text-subtitle-large">
                        Performance Insights
                      </CardTitle>
                      <CardDescription className="text-subtitle">
                        AI-powered insights from your performance data.
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <Card
                          variant="glass-subtle"
                          className="p-4 border border-emerald-500/30"
                        >
                          <div className="flex items-center space-x-2 text-emerald-700 dark:text-emerald-300">
                            <TrendingUp className="h-4 w-4" />
                            <span className="font-medium">
                              Strong Performance
                            </span>
                          </div>
                          <p className="text-sm text-emerald-600 dark:text-emerald-400 mt-1">
                            Your conversion rate has improved by 15% this
                            quarter, outperforming industry averages.
                          </p>
                        </Card>

                        <Card
                          variant="glass-subtle"
                          className="p-4 border border-blue-500/30"
                        >
                          <div className="flex items-center space-x-2 text-blue-700 dark:text-blue-300">
                            <BarChart3 className="h-4 w-4" />
                            <span className="font-medium">Market Insight</span>
                          </div>
                          <p className="text-sm text-blue-600 dark:text-blue-400 mt-1">
                            Technology and food service businesses are showing
                            40% higher demand this month.
                          </p>
                        </Card>

                        <Card
                          variant="glass-subtle"
                          className="p-4 border border-purple-500/30"
                        >
                          <div className="flex items-center space-x-2 text-purple-700 dark:text-purple-300">
                            <Users className="h-4 w-4" />
                            <span className="font-medium">
                              Engagement Trend
                            </span>
                          </div>
                          <p className="text-sm text-purple-600 dark:text-purple-400 mt-1">
                            Listings with detailed descriptions receive 60% more
                            inquiries on average.
                          </p>
                        </Card>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
