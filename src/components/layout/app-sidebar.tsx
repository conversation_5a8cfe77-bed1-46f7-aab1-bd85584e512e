import React from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useAuth, useWorkspace } from "@/contexts/AuthContext";
import { cn } from "@/lib/utils";

// Icons
import {
  LayoutDashboard,
  Building2,
  Package,
  Plus,
  BarChart3,
  Users,
  Settings,
  LogOut,
  Menu,
  X,
} from "lucide-react";

// Navigation items configuration
const navigationSections = [
  {
    title: "Main",
    items: [
      {
        label: "Dashboard",
        path: "/dashboard",
        icon: LayoutDashboard,
      },
    ],
  },
  {
    title: "Business",
    items: [
      {
        label: "Listings",
        path: "/listings",
        icon: Package,
      },

      {
        label: "Add Listing",
        path: "/listings/new",
        icon: Plus,
      },
      {
        label: "Reports",
        path: "/reports",
        icon: BarChart3,
      },
    ],
  },
  {
    title: "Workspace",
    items: [
      {
        label: "Team",
        path: "/team",
        icon: Users,
      },
      {
        label: "Workspace Settings",
        path: "/workspace/settings",
        icon: Building2,
      },
    ],
  },
  {
    title: "Account",
    items: [
      {
        label: "Settings",
        path: "/settings",
        icon: Settings,
      },
    ],
  },
];

interface NavigationItemProps {
  item: {
    label: string;
    path: string;
    icon: React.ComponentType<{ className?: string }>;
  };
  isActive: boolean;
  isCollapsed: boolean;
  onClick: () => void;
}

const NavigationItem: React.FC<NavigationItemProps> = ({ item, isActive, isCollapsed, onClick }) => {
  const Icon = item.icon;

  return (
    <button
      onClick={onClick}
      className={cn(
        "w-full flex items-center gap-3 px-3 py-2 text-left rounded-lg transition-all duration-150",
        "hover:bg-white/10 dark:hover:bg-black/10 hover:shadow-md hover:backdrop-blur-sm",
        "focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2",
        isActive && "glass-card-subtle text-primary font-medium border border-primary/20 shadow-md shadow-primary/5",
        isCollapsed && "justify-center px-2"
      )}
      title={isCollapsed ? item.label : undefined}
    >
      <Icon className="h-5 w-5 flex-shrink-0" />
      {!isCollapsed && <span className="truncate">{item.label}</span>}
    </button>
  );
};

interface SidebarProps {
  className?: string;
}

const AppSidebar: React.FC<SidebarProps> = ({ className }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { signOut, profile } = useAuth();
  const { workspace } = useWorkspace();
  const [isCollapsed, setIsCollapsed] = React.useState(false);
  const [isMobileOpen, setIsMobileOpen] = React.useState(false);

  const handleNavigation = (path: string) => {
    navigate(path);
    setIsMobileOpen(false); // Close mobile sidebar on navigation
  };

  const handleLogout = async () => {
    console.log('🚪 [AppSidebar] handleLogout called');
    try {
      console.log('[AppSidebar] Starting signOut...');
      await signOut();
      console.log('[AppSidebar] signOut completed successfully');
      console.log('[AppSidebar] Navigating to "/" with replace: true');
      navigate('/', { replace: true });
      console.log('[AppSidebar] Navigation completed');
    } catch (error) {
      console.error('[AppSidebar] Logout error:', error);
      console.log('[AppSidebar] Navigating to "/" anyway (error fallback)');
      // Navigate anyway to ensure user gets logged out from UI
      navigate('/', { replace: true });
      console.log('[AppSidebar] Fallback navigation completed');
    }
  };

  const toggleCollapsed = () => {
    setIsCollapsed(!isCollapsed);
  };

  const toggleMobile = () => {
    setIsMobileOpen(!isMobileOpen);
  };

  return (
    <>
      {/* Mobile overlay */}
      {isMobileOpen && (
        <div 
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={() => setIsMobileOpen(false)}
        />
      )}
      
      {/* Mobile toggle button */}
      <button
        onClick={toggleMobile}
        className="fixed top-4 left-4 z-50 lg:hidden bg-background border border-border rounded-md p-2 shadow-lg"
      >
        {isMobileOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
      </button>

      {/* Glassmorphic Sidebar */}
      <div
        className={cn(
          "fixed inset-y-0 left-0 z-50 flex flex-col glass-card-subtle border-r border-white/20 transition-all duration-300 shadow-2xl",
          "lg:relative lg:translate-x-0",
          isCollapsed ? "w-16" : "w-64",
          isMobileOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0",
          className
        )}
      >
        {/* Glassmorphic Header */}
        <div
          className="flex items-center justify-center p-4 border-b border-white/20 cursor-pointer hover:bg-white/5 transition-all duration-150"
          onClick={toggleCollapsed}
          title={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          {!isCollapsed && (
            <img
              src="/lovable-uploads/8e99d9d7-748b-4512-8bd7-54ca83e3b8dd.png"
              alt="Rendyr Logo"
              className="h-8"
            />
          )}
        </div>

        {/* Navigation */}
        <nav className="flex-1 overflow-y-auto p-4">
          <div className="space-y-6">
            {navigationSections.map((section) => (
              <div key={section.title}>
                {!isCollapsed && (
                  <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-3">
                    {section.title}
                  </h3>
                )}
                <div className="space-y-1">
                  {section.items.map((item) => (
                    <NavigationItem
                      key={item.path}
                      item={item}
                      isActive={location.pathname === item.path}
                      isCollapsed={isCollapsed}
                      onClick={() => handleNavigation(item.path)}
                    />
                  ))}
                </div>
              </div>
            ))}
          </div>
        </nav>

        {/* Footer */}
        <div className="border-t border-border p-4">
          <button
            onClick={handleLogout}
            className={cn(
              "w-full flex items-center gap-3 px-3 py-2 text-left rounded-lg transition-colors",
              "hover:bg-destructive/10 hover:text-destructive",
              "focus:outline-none focus:ring-2 focus:ring-destructive focus:ring-offset-2",
              isCollapsed && "justify-center px-2"
            )}
            title={isCollapsed ? "Sign out" : undefined}
          >
            <LogOut className="h-5 w-5 flex-shrink-0" />
            {!isCollapsed && <span>Sign out</span>}
          </button>
        </div>
      </div>
    </>
  );
};

export default AppSidebar;