import React, { useState } from "react";
import { <PERSON><PERSON><PERSON>, Building2, <PERSON><PERSON><PERSON>, ArrowRightLeft } from "lucide-react";
import { usePermissions } from "@/hooks/usePermissions";
import { UserRole } from "@/types";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Shield } from "lucide-react";
import { RoleGuard } from "@/components/auth/RoleGuard";
import { OrganizationSettings } from "./OrganizationSettings";
import { WorkspaceConfiguration } from "./WorkspaceConfiguration";
import { WorkspaceSwitcher } from "./WorkspaceSwitcher";
import { BillingSettings } from "./BillingSettings";
import { useWorkspaceSwitcher } from "@/hooks/useWorkspaceSwitcher";

interface WorkspaceSettingsProps {
  className?: string;
}

export const WorkspaceSettings: React.FC<WorkspaceSettingsProps> = ({
  className,
}) => {
  const { canViewWorkspaceSettings, canManageBilling } = usePermissions();
  const { availableWorkspaces } = useWorkspaceSwitcher();
  const [activeTab, setActiveTab] = useState("organization");

  // Show workspace tab only if user has access to multiple workspaces
  const hasMultipleWorkspaces = availableWorkspaces.length > 0;

  // Check permissions
  if (!canViewWorkspaceSettings()) {
    return (
      <Card className="max-w-md mx-auto">
        <CardContent className="pt-6">
          <div className="text-center">
            <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Access Denied</h3>
            <p className="text-muted-foreground">
              You don't have permission to view workspace settings.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={className}>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Workspace Settings
          </h1>
          <p className="text-muted-foreground">
            Manage your workspace configuration, branding, and subscription.
          </p>
        </div>

        {/* Settings Tabs */}
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="space-y-6"
        >
          <TabsList
            className={`grid w-full ${
              hasMultipleWorkspaces ? "grid-cols-4" : "grid-cols-3"
            }`}
          >
            <TabsTrigger
              value="organization"
              className="flex items-center space-x-2"
            >
              <Building2 className="h-4 w-4" />
              <span>Organization</span>
            </TabsTrigger>
            <TabsTrigger
              value="configuration"
              className="flex items-center space-x-2"
            >
              <Settings className="h-4 w-4" />
              <span>Configuration</span>
            </TabsTrigger>
            {hasMultipleWorkspaces && (
              <TabsTrigger
                value="workspace"
                className="flex items-center space-x-2"
              >
                <ArrowRightLeft className="h-4 w-4" />
                <span>Workspace</span>
              </TabsTrigger>
            )}
            <TabsTrigger
              value="billing"
              className="flex items-center space-x-2"
              disabled={!canManageBilling()}
            >
              <CreditCard className="h-4 w-4" />
              <span>Billing</span>
            </TabsTrigger>
          </TabsList>

          {/* Organization Settings Tab */}
          <TabsContent value="organization" className="space-y-6">
            <OrganizationSettings />
          </TabsContent>

          {/* Workspace Configuration Tab */}
          <TabsContent value="configuration" className="space-y-6">
            <WorkspaceConfiguration />
          </TabsContent>

          {/* Workspace Switcher Tab - only shown if user has multiple workspaces */}
          {hasMultipleWorkspaces && (
            <TabsContent value="workspace" className="space-y-6">
              <WorkspaceSwitcher />
            </TabsContent>
          )}

          {/* Billing & Subscription Tab */}
          <TabsContent value="billing" className="space-y-6">
            <RoleGuard
              allowedRoles={[UserRole.OWNER, UserRole.ADMIN]}
              fallback={
                <Alert>
                  <Shield className="h-4 w-4" />
                  <AlertDescription>
                    You need admin privileges to manage billing settings.
                  </AlertDescription>
                </Alert>
              }
            >
              <BillingSettings />
            </RoleGuard>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};