import React from "react";
import { <PERSON> } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { LoadingOverlay } from "@/components/ui/loading-overlay";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  TrendingUp,
  TrendingDown,
  Plus,
  Building2,
  DollarSign,
  Users,
  Calendar,
  Target,
  Award,
  Eye,
  ArrowUpRight,
  ArrowDownRight,
  Activity,
  Clock,
  CheckCircle,
  AlertCircle,
  BarChart3,
  PieChart,
  Zap,
  Star,
  Filter,
  Search,
  Bell,
  Settings
} from "lucide-react";
import { useListings } from "@/hooks/useQueryApi";
import { getStatusBadgeVariant, getStatusLabel } from "@/lib/status-utils";

const V2_Dashboard: React.FC = () => {
  const { listings, loading, error } = useListings({ 
    limit: 12, 
    sortBy: 'created_at', 
    sortOrder: 'asc' 
  });

  const kpiData = React.useMemo(() => {
    if (loading || !listings.length) {
      return {
        activeListings: 0,
        underContract: 0,
        closedThisMonth: 0,
        pipelineCommissions: 0,
        totalRevenue: 0,
        avgDeal: 0,
        conversionRate: 0,
        avgDaysToClose: 0,
      };
    }

    const activeListings = listings.filter(l => l.status === 'Active').length;
    const underContract = listings.filter(l => l.status === 'Under Contract').length;
    const closedThisMonth = listings.filter(l => l.status === 'Sold').length;
    
    const totalRevenue = listings
      .filter(l => l.status === 'Sold')
      .reduce((sum, listing) => sum + (listing.askingPrice || 0), 0);
      
    const pipelineCommissions = listings
      .filter(l => l.status === 'Active' || l.status === 'Under Contract')
      .reduce((sum, listing) => sum + (listing.askingPrice || 0) * 0.03, 0);

    const avgDeal = closedThisMonth > 0 ? totalRevenue / closedThisMonth : 0;
    const conversionRate = activeListings > 0 ? (closedThisMonth / activeListings) * 100 : 0;
    const avgDaysToClose = 45; // Mock data

    return {
      activeListings,
      underContract,
      closedThisMonth,
      pipelineCommissions,
      totalRevenue,
      avgDeal,
      conversionRate,
      avgDaysToClose,
    };
  }, [listings, loading]);



  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };



  return (
    <div className="min-h-screen texture-bg">
      {/* Glassmorphic Header */}
      <div className="glass-card-subtle border-b border-white/20 sticky top-0 z-10">
        <div className="px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 glass-card rounded-xl flex items-center justify-center">
                  <BarChart3 className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <h1 className="text-title font-bold text-foreground">Dashboard</h1>
                  <p className="text-subtitle text-muted-foreground">Welcome back! Here's your business overview</p>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Button variant="glass" size="sm" className="hidden sm:flex">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>
              <Button variant="glass" size="sm" className="hidden sm:flex">
                <Bell className="w-4 h-4" />
              </Button>
              <Button variant="glass-primary" size="touch" className="shadow-lg shadow-primary/25" asChild>
                <Link to="/listings/new">
                  <Plus className="w-4 h-4 mr-2" />
                  New Listing
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="px-4 sm:px-6 lg:px-8 py-6 space-y-6">
        {/* Enhanced Glassmorphic KPI Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
          {/* Active Listings */}
          <Card variant="glass" className="relative overflow-hidden shadow-lg shadow-emerald-500/5 hover:shadow-xl hover:shadow-emerald-500/10">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <p className="text-subtitle font-medium text-muted-foreground">Active Listings</p>
                  <p className="text-2xl md:text-3xl font-bold text-foreground">{kpiData.activeListings}</p>
                  <div className="flex items-center space-x-1 text-sm">
                    <TrendingUp className="w-4 h-4 text-emerald-500" />
                    <span className="text-emerald-600 font-medium">+12%</span>
                    <span className="text-muted-foreground">vs last month</span>
                  </div>
                </div>
                <div className="w-12 h-12 glass-card rounded-xl flex items-center justify-center">
                  <Building2 className="w-6 h-6 text-emerald-500" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Under Contract */}
          <Card variant="glass" className="relative overflow-hidden shadow-lg shadow-amber-500/5 hover:shadow-xl hover:shadow-amber-500/10">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <p className="text-subtitle font-medium text-muted-foreground">Under Contract</p>
                  <p className="text-2xl md:text-3xl font-bold text-foreground">{kpiData.underContract}</p>
                  <div className="flex items-center space-x-1 text-sm">
                    <Clock className="w-4 h-4 text-amber-500" />
                    <span className="text-amber-600 font-medium">Avg {kpiData.avgDaysToClose} days</span>
                  </div>
                </div>
                <div className="w-12 h-12 glass-card rounded-xl flex items-center justify-center">
                  <Clock className="w-6 h-6 text-amber-500" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Closed This Month */}
          <Card variant="glass" className="relative overflow-hidden shadow-lg shadow-blue-500/5 hover:shadow-xl hover:shadow-blue-500/10">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <p className="text-subtitle font-medium text-muted-foreground">Closed This Month</p>
                  <p className="text-2xl md:text-3xl font-bold text-foreground">{kpiData.closedThisMonth}</p>
                  <div className="flex items-center space-x-1 text-sm">
                    <Target className="w-4 h-4 text-blue-500" />
                    <span className="text-blue-600 font-medium">{kpiData.conversionRate.toFixed(1)}%</span>
                    <span className="text-muted-foreground">conversion</span>
                  </div>
                </div>
                <div className="w-12 h-12 glass-card rounded-xl flex items-center justify-center">
                  <CheckCircle className="w-6 h-6 text-blue-500" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Total Revenue */}
          <Card variant="glass" className="relative overflow-hidden shadow-lg shadow-purple-500/5 hover:shadow-xl hover:shadow-purple-500/10">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <p className="text-subtitle font-medium text-muted-foreground">Total Revenue</p>
                  <p className="text-2xl md:text-3xl font-bold text-foreground">{formatCurrency(kpiData.totalRevenue)}</p>
                  <div className="flex items-center space-x-1 text-sm">
                    <TrendingUp className="w-4 h-4 text-purple-500" />
                    <span className="text-purple-600 font-medium">+25%</span>
                    <span className="text-muted-foreground">vs last month</span>
                  </div>
                </div>
                <div className="w-12 h-12 glass-card rounded-xl flex items-center justify-center">
                  <DollarSign className="w-6 h-6 text-purple-500" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Performance Metrics Row */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Pipeline Value */}
          <Card variant="glass" className="shadow-lg shadow-primary/5">
            <CardHeader className="pb-3">
              <CardTitle className="text-subtitle-large font-semibold flex items-center space-x-2">
                <PieChart className="w-5 h-5 text-primary" />
                <span>Pipeline Value</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-2xl font-bold text-foreground">{formatCurrency(kpiData.pipelineCommissions)}</span>
                <Badge variant="glass" className="border-primary/20 text-primary">
                  Expected Commission
                </Badge>
              </div>
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Progress to Goal</span>
                  <span className="font-medium text-foreground">75%</span>
                </div>
                <Progress value={75} className="h-2" />
                <p className="text-xs text-muted-foreground">Target: {formatCurrency(kpiData.pipelineCommissions * 1.33)}</p>
              </div>
            </CardContent>
          </Card>

          {/* Average Deal Size */}
          <Card variant="glass" className="shadow-lg shadow-indigo-500/5">
            <CardHeader className="pb-3">
              <CardTitle className="text-subtitle-large font-semibold flex items-center space-x-2">
                <Target className="w-5 h-5 text-indigo-600" />
                <span>Deal Analytics</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Avg Deal Size</p>
                  <p className="text-xl font-bold text-foreground">{formatCurrency(kpiData.avgDeal)}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Conversion Rate</p>
                  <p className="text-xl font-bold text-foreground">{kpiData.conversionRate.toFixed(1)}%</p>
                </div>
              </div>
              <div className="pt-2 border-t border-white/20">
                <div className="flex items-center space-x-2 text-sm">
                  <Star className="w-4 h-4 text-yellow-500" />
                  <span className="text-muted-foreground">Performance trending upward</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
          {/* Recent Listings - Takes up 2 columns on xl screens */}
          <Card variant="glass" className="xl:col-span-2 shadow-xl">
            <CardHeader className="border-b border-white/20">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 glass-card rounded-lg flex items-center justify-center">
                    <Eye className="w-4 h-4 text-primary" />
                  </div>
                  <div>
                    <CardTitle className="text-subtitle-large">Recent Listings</CardTitle>
                    <CardDescription className="text-subtitle">Your latest business opportunities</CardDescription>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="glass" size="sm" className="hidden sm:flex">
                    <Search className="w-4 h-4 mr-2" />
                    Search
                  </Button>
                  <Button variant="glass-primary" size="sm" asChild>
                    <Link to="/listings">View All →</Link>
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-0 relative">
              {/* Loading overlay for listings section only */}
              {loading && (
                <div className="absolute inset-0 z-10">
                  <LoadingOverlay
                    overlay={false}
                    className="absolute inset-0 rounded-b-lg"
                    source="Dashboard:/dashboard:ListingsSection"
                  />
                </div>
              )}

              {/* Error state for listings */}
              {error && !loading && (
                <div className="p-6">
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      Failed to load listings. Please try refreshing the page.
                    </AlertDescription>
                  </Alert>
                </div>
              )}

              {!loading && !error && listings.length === 0 ? (
                <div className="flex flex-col items-center justify-center p-12 text-center">
                  <div className="w-10 h-10 glass-card rounded-lg flex items-center justify-center">
                    <Building2 className="h-5 w-5 text-muted-foreground" />
                  </div>
                  <h3 className="mt-4 text-subtitle-large font-semibold text-foreground">No listings yet</h3>
                  <p className="mt-1 text-subtitle text-muted-foreground">Create your first listing to see it here.</p>
                  <Button variant="glass-primary" className="mt-4" asChild>
                    <Link to="/listings/new">
                      <Plus className="mr-2 h-4 w-4" />
                      Create Listing
                    </Link>
                  </Button>
                </div>
              ) : !loading && !error ? (
                <div className="divide-y divide-white/10">
                  {listings.slice(0, 8).map((listing, index) => (
                    <div key={listing.id} className="p-4 hover:bg-white/5 dark:hover:bg-black/5 transition-all duration-300 rounded-lg mx-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4 min-w-0 flex-1">
                          <div className="w-10 h-10 glass-card-subtle rounded-lg flex items-center justify-center flex-shrink-0">
                            <Building2 className="w-5 h-5 text-primary" />
                          </div>
                          <div className="min-w-0 flex-1">
                            <h4 className="font-medium text-foreground truncate">
                              {listing.businessName || `Business ${index + 1}`}
                            </h4>
                            <p className="text-sm text-muted-foreground truncate">
                              {listing.industry || 'Various Industry'}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-4 flex-shrink-0">
                          <div className="text-right hidden sm:block">
                            <p className="font-semibold text-foreground">{formatCurrency(listing.askingPrice || 0)}</p>
                            <p className="text-xs text-muted-foreground">Asking Price</p>
                          </div>
                          <Badge variant={getStatusBadgeVariant(listing.status)} className="text-xs font-medium">
                            {getStatusLabel(listing.status)}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : null}
            </CardContent>
          </Card>

          {/* Quick Actions & Insights */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <Card variant="glass" className="shadow-lg shadow-orange-500/5">
              <CardHeader className="pb-3">
                <CardTitle className="text-subtitle-large font-semibold flex items-center space-x-2">
                  <Zap className="w-5 h-5 text-orange-500" />
                  <span>Quick Actions</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="glass-primary" size="touch" className="w-full justify-start shadow-md shadow-primary/10" asChild>
                  <Link to="/listings/new">
                    <Plus className="w-4 h-4 mr-3" />
                    Create New Listing
                  </Link>
                </Button>
                <Button variant="glass" size="touch" className="w-full justify-start" asChild>
                  <Link to="/team">
                    <Users className="w-4 h-4 mr-3" />
                    Manage Team
                  </Link>
                </Button>
                <Button variant="glass" size="touch" className="w-full justify-start" asChild>
                  <Link to="/reports">
                    <BarChart3 className="w-4 h-4 mr-3" />
                    View Reports
                  </Link>
                </Button>
                <Button variant="glass" size="touch" className="w-full justify-start" asChild>
                  <Link to="/settings">
                    <Settings className="w-4 h-4 mr-3" />
                    Settings
                  </Link>
                </Button>
              </CardContent>
            </Card>

            {/* Monthly Goals */}
            <Card variant="glass" className="shadow-lg shadow-primary/10 border border-primary/20">
              <CardHeader className="pb-3">
                <CardTitle className="text-subtitle-large font-semibold flex items-center space-x-2">
                  <Award className="w-5 h-5 text-primary" />
                  <span>Monthly Goals</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span className="font-medium text-foreground">Listings Target</span>
                      <span className="text-muted-foreground">{kpiData.activeListings}/20</span>
                    </div>
                    <Progress value={(kpiData.activeListings / 20) * 100} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span className="font-medium text-foreground">Revenue Goal</span>
                      <span className="text-muted-foreground">{Math.round((kpiData.totalRevenue / 100000) * 100)}%</span>
                    </div>
                    <Progress value={(kpiData.totalRevenue / 100000) * 100} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span className="font-medium text-foreground">Deals Closed</span>
                      <span className="text-muted-foreground">{kpiData.closedThisMonth}/10</span>
                    </div>
                    <Progress value={(kpiData.closedThisMonth / 10) * 100} className="h-2" />
                  </div>
                </div>
                <div className="pt-3 border-t border-primary/20">
                  <div className="flex items-center space-x-2 text-sm">
                    <TrendingUp className="w-4 h-4 text-primary" />
                    <span className="text-primary font-medium">On track to exceed goals!</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default V2_Dashboard;
