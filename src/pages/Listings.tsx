import React, { useState } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Plus,
  Search,
  Filter,
  MapPin,
  Building2,
  Edit,
  Upload,
  SortAsc,
  SortDesc,
  X,
  FilterX,
  Trash2,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { getStatusBadgeVariant, getStatusLabel } from "@/lib/status-utils";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { formatCurrencyAbbreviated } from "@/lib/formatters";
import { useListings, useDeleteListingMutation } from "@/hooks/useQueryApi";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { LoadingOverlay, ButtonSpinner } from "@/components/ui/loading-overlay";
import CsvImportModal2 from "@/components/forms/CsvImportModal2";
import { cn } from "@/lib/utils";

// Filter interface
interface ListingFilters {
  status: string;
  industry: string;
  minPrice: string;
  maxPrice: string;
  location: string;
  assignedTo: string;
  sortBy: string;
  sortOrder: string;
}

// Sort options
const sortOptions = [
  { value: "created_at:desc", label: "Newest First", icon: SortDesc },
  { value: "created_at:asc", label: "Oldest First", icon: SortAsc },
  { value: "asking_price:desc", label: "Price: High to Low", icon: SortDesc },
  { value: "asking_price:asc", label: "Price: Low to High", icon: SortAsc },
  { value: "business_name:asc", label: "Name: A-Z", icon: SortAsc },
  { value: "business_name:desc", label: "Name: Z-A", icon: SortDesc },
];

const V2_Listings: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [filterModalOpen, setFilterModalOpen] = useState(false);
  const [filters, setFilters] = useState<ListingFilters>({
    status: "all",
    industry: "all",
    minPrice: "",
    maxPrice: "",
    location: "",
    assignedTo: "all",
    sortBy: "created_at",
    sortOrder: "desc",
  });
  const itemsPerPage = 12;
  const navigate = useNavigate();

  // Delete dialog state
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [listingToDelete, setListingToDelete] = useState<any>(null);

  // Delete mutation
  const deleteMutation = useDeleteListingMutation({
    onSuccess: () => {
      setShowDeleteDialog(false);
      setListingToDelete(null);
    },
  });

  // Get current sort value for display
  const currentSortValue = `${filters.sortBy}:${filters.sortOrder}`;
  const currentSortOption =
    sortOptions.find((option) => option.value === currentSortValue) ||
    sortOptions[0];

  // Build query parameters from filters
  const queryParams = React.useMemo(() => {
    const params: any = {
      page: currentPage,
      limit: itemsPerPage,
      search: searchTerm || undefined,
      sortBy: filters.sortBy,
      sortOrder: filters.sortOrder,
    };

    if (filters.status && filters.status !== "all")
      params.status = filters.status;
    if (filters.industry && filters.industry !== "all")
      params.industry = filters.industry;
    if (filters.location) params.location = filters.location;
    if (filters.assignedTo && filters.assignedTo !== "all")
      params.assignedTo = filters.assignedTo;

    // Handle price range
    if (filters.minPrice) {
      const minPrice = parseFloat(filters.minPrice.replace(/[,$]/g, ""));
      if (!isNaN(minPrice)) params.minPrice = minPrice;
    }
    if (filters.maxPrice) {
      const maxPrice = parseFloat(filters.maxPrice.replace(/[,$]/g, ""));
      if (!isNaN(maxPrice)) params.maxPrice = maxPrice;
    }

    return params;
  }, [currentPage, itemsPerPage, searchTerm, filters]);

  // Use React Query hook for listings
  const { listings, pagination, loading, error, isRefetching, refetch } =
    useListings(queryParams);

  // Calculate total pages from API pagination
  const totalPages =
    (pagination as any).totalPages || (pagination as any).pages || 1;

  // Reset to first page when search or filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, filters]);

  // Helper function to update filters
  const updateFilter = (key: keyof ListingFilters, value: string) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
  };

  // Handle sort change
  const handleSortChange = (value: string) => {
    const [sortBy, sortOrder] = value.split(":");
    setFilters((prev) => ({ ...prev, sortBy, sortOrder }));
  };

  // Clear all filters
  const clearFilters = () => {
    setFilters({
      status: "all",
      industry: "all",
      minPrice: "",
      maxPrice: "",
      location: "",
      assignedTo: "all",
      sortBy: "created_at",
      sortOrder: "desc",
    });
  };

  // Check if any filters are active
  const hasActiveFilters = Object.entries(filters).some(
    ([key, value]) =>
      key !== "sortBy" && key !== "sortOrder" && value !== "" && value !== "all"
  );

  // Count active filters
  const activeFilterCount = Object.entries(filters).filter(
    ([key, value]) =>
      key !== "sortBy" && key !== "sortOrder" && value !== "" && value !== "all"
  ).length;

  // Glassmorphic status badge component
  const StatusBadge = ({ status }: { status: string }) => {
    return (
      <Badge
        variant={getStatusBadgeVariant(status)}
        className="text-xs font-medium"
      >
        {getStatusLabel(status)}
      </Badge>
    );
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleDeleteClick = (e: React.MouseEvent, listing: any) => {
    e.stopPropagation();
    setListingToDelete(listing);
    setShowDeleteDialog(true);
  };

  return (
    <div className="min-h-screen bg-white dark:bg-slate-900">
      {/* Header Section - Enhanced Glassmorphic Design */}
      <div className="sticky top-0 z-40 bg-gradient-to-r from-white/90 via-white/85 to-white/90 dark:from-slate-900/90 dark:via-slate-900/85 dark:to-slate-900/90 backdrop-blur-2xl border-b border-white/20 dark:border-slate-700/30 shadow-lg shadow-black/5">
        <div className="container mx-auto px-4 sm:px-6 py-6 sm:py-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 sm:gap-6">
            <div className="space-y-2">
              <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-slate-900 via-blue-900 to-slate-600 dark:from-white dark:via-blue-100 dark:to-slate-200 bg-clip-text text-transparent">
                Business Listings
              </h1>
              <p className="text-slate-600 dark:text-slate-300 text-sm sm:text-base">
                Discover and manage premium business opportunities
              </p>
            </div>

            <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4">
              <CsvImportModal2 onSuccess={() => refetch()}>
                <Button
                  variant="outline"
                  className="gap-2 h-10 px-4 sm:h-12 sm:px-6 bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border-white/30 dark:border-slate-600/30 
                  hover:bg-slate-50/80 dark:hover:bg-slate-700/80 hover:shadow-md hover:backdrop-blur-sm
                  shadow-lg shadow-black/5 transition-all duration-150 hover:scale-105
                  focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2"
                >
                  <Upload className="h-4 w-4 sm:h-5 sm:w-5" />
                  <span className="hidden sm:inline">Import CSV</span>
                  <span className="sm:hidden">Import</span>
                </Button>
              </CsvImportModal2>

              <Button
                className="gap-2 h-10 px-4 sm:h-12 sm:px-6 bg-gradient-to-r from-blue-600/90 via-indigo-600/90 to-purple-600/90 hover:from-blue-700/95 hover:via-indigo-700/95 hover:to-purple-700/95 text-white shadow-xl shadow-blue-500/25 backdrop-blur-sm border border-white/20 transition-all duration-300 hover:scale-105 hover:shadow-2xl"
                asChild
              >
                <Link to="/listings/new">
                  <Plus className="h-4 w-4 sm:h-5 sm:w-5" />
                  <span className="hidden sm:inline">New Listing</span>
                  <span className="sm:hidden">New</span>
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-8">
        {/* Search and Controls - Enhanced Glassmorphic Design */}
        <div className="mb-8 space-y-6">
          <div className="flex flex-col lg:flex-row gap-6">
            {/* Enhanced Search Bar */}
            <div className="relative flex-1">
              <Search className="absolute left-4 top-1/2 h-5 w-5 -translate-y-1/2 text-slate-400 dark:text-slate-500" />
              <Input
                type="search"
                placeholder="Search by business name, industry, or location..."
                className="pl-12 h-14 bg-white/60 dark:bg-slate-800/60 backdrop-blur-xl border-white/40 dark:border-slate-600/40 focus:bg-white/80 dark:focus:bg-slate-700/80 transition-all duration-300 text-base shadow-lg shadow-black/5 hover:shadow-xl focus:shadow-xl focus:border-blue-400/50 dark:focus:border-blue-500/50"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            {/* Enhanced Controls */}
            <div className="flex items-center gap-4">
              {/* Enhanced Sort Dropdown */}
              <Select value={currentSortValue} onValueChange={handleSortChange}>
                <SelectTrigger className="w-52 h-14 bg-white/60 dark:bg-slate-800/60 backdrop-blur-xl border-white/40 dark:border-slate-600/40 shadow-lg shadow-black/5 hover:shadow-xl transition-all duration-300 hover:bg-white/80 dark:hover:bg-slate-700/60">
                  <div className="flex items-center gap-2">
                    <currentSortOption.icon className="h-5 w-5" />
                    <span className="font-medium">
                      {currentSortOption.label}
                    </span>
                  </div>
                </SelectTrigger>
                <SelectContent className="bg-white/90 dark:bg-slate-800/90 backdrop-blur-xl border-white/40 dark:border-slate-600/40">
                  {sortOptions.map((option) => {
                    const IconComponent = option.icon;
                    return (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex items-center gap-2">
                          <IconComponent className="h-4 w-4" />
                          {option.label}
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>

              {/* Enhanced Filter Button */}
              <Popover open={filterModalOpen} onOpenChange={setFilterModalOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "gap-2 h-14 px-6 bg-white/60 dark:bg-slate-800/60 backdrop-blur-xl border-white/40 dark:border-slate-600/40 shadow-lg shadow-black/5 hover:shadow-md hover:backdrop-blur-sm hover:bg-slate-50/80 dark:hover:bg-slate-700/80 transition-all duration-150 focus:outline-none focus:ring-2 focus:ring-primary/50 hover:scale-105 focus:ring-offset-2",
                      hasActiveFilters &&
                        "bg-blue-100/80 dark:bg-blue-900/40 border-blue-300/60 dark:border-blue-600/60 shadow-blue-500/20"
                    )}
                  >
                    <Filter className="h-5 w-5" />
                    <span className="font-medium">Filter</span>
                    {hasActiveFilters && (
                      <Badge
                        variant="secondary"
                        className="ml-1 h-6 w-6 p-0 text-xs flex items-center justify-center bg-blue-600 text-white"
                      >
                        {activeFilterCount}
                      </Badge>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent
                  className="w-80 p-6 bg-white/90 dark:bg-slate-800/90 backdrop-blur-2xl border-white/40 dark:border-slate-600/40 shadow-2xl shadow-black/10"
                  align="end"
                  sideOffset={12}
                >
                  <div className="space-y-6">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                      <h4 className="font-semibold text-lg">Filter Listings</h4>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setFilterModalOpen(false)}
                        className="h-8 w-8 p-0"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>

                    {/* Status Filter */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Status</Label>
                      <Select
                        value={filters.status}
                        onValueChange={(value) => updateFilter("status", value)}
                      >
                        <SelectTrigger className="h-10">
                          <SelectValue placeholder="All Statuses" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Statuses</SelectItem>
                          <SelectItem value="active">Active</SelectItem>
                          <SelectItem value="under contract">
                            Under Contract
                          </SelectItem>
                          <SelectItem value="sold">Sold</SelectItem>
                          <SelectItem value="confidential">
                            Confidential
                          </SelectItem>
                          <SelectItem value="archived">Archived</SelectItem>
                          <SelectItem value="draft">Draft</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Industry Filter */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Industry</Label>
                      <Select
                        value={filters.industry}
                        onValueChange={(value) =>
                          updateFilter("industry", value)
                        }
                      >
                        <SelectTrigger className="h-10">
                          <SelectValue placeholder="All Industries" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Industries</SelectItem>
                          <SelectItem value="Restaurant">Restaurant</SelectItem>
                          <SelectItem value="Technology">Technology</SelectItem>
                          <SelectItem value="Healthcare">Healthcare</SelectItem>
                          <SelectItem value="Retail">Retail</SelectItem>
                          <SelectItem value="Manufacturing">
                            Manufacturing
                          </SelectItem>
                          <SelectItem value="Professional Services">
                            Professional Services
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Price Range */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Price Range</Label>
                      <div className="grid grid-cols-2 gap-3">
                        <Input
                          type="text"
                          inputMode="decimal"
                          placeholder="Min price"
                          value={filters.minPrice}
                          onChange={(e) =>
                            updateFilter("minPrice", e.target.value)
                          }
                          className="h-10"
                        />
                        <Input
                          type="text"
                          inputMode="decimal"
                          placeholder="Max price"
                          value={filters.maxPrice}
                          onChange={(e) =>
                            updateFilter("maxPrice", e.target.value)
                          }
                          className="h-10"
                        />
                      </div>
                    </div>

                    {/* Location Filter */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Location</Label>
                      <Input
                        type="text"
                        placeholder="City, State"
                        value={filters.location}
                        onChange={(e) =>
                          updateFilter("location", e.target.value)
                        }
                        className="h-10"
                      />
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-3 pt-4 border-t border-slate-200 dark:border-slate-700">
                      <Button
                        variant="outline"
                        onClick={clearFilters}
                        disabled={!hasActiveFilters}
                        className="flex-1"
                      >
                        <FilterX className="h-4 w-4 mr-2" />
                        Clear
                      </Button>
                      <Button
                        onClick={() => setFilterModalOpen(false)}
                        className="flex-1 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
                      >
                        Apply Filters
                      </Button>
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
            </div>
          </div>

          {/* Results Summary */}
          <div className="flex items-center justify-between text-sm text-slate-600 dark:text-slate-400">
            <div className="flex items-center gap-2">
              {loading ? (
                <span>Loading listings...</span>
              ) : (
                <span>
                  Showing {(currentPage - 1) * itemsPerPage + 1}-
                  {Math.min(currentPage * itemsPerPage, pagination.total)} of{" "}
                  {pagination.total} listings
                </span>
              )}
              {isRefetching && <ButtonSpinner className="h-3 w-3" />}
            </div>

            {hasActiveFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
              >
                Clear all filters
              </Button>
            )}
          </div>
        </div>

        {/* Error State */}
        {error && (
          <Alert className="mb-6 border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20">
            <AlertDescription className="text-red-800 dark:text-red-200">
              Failed to load listings.{" "}
              <Button
                variant="link"
                className="p-0 h-auto text-red-600 dark:text-red-400"
                onClick={() => refetch()}
              >
                Try again
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Loading State */}
        {loading ? (
          <LoadingOverlay
            message="Loading your business listings..."
            overlay={false}
            source="Listings:/listings:DataFetch"
          />
        ) : listings.length === 0 ? (
          /* Empty State */
          <div className="flex flex-col items-center justify-center py-20 text-center">
            <div className="rounded-full bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-900/20 p-6 mb-6">
              <Building2 className="h-12 w-12 text-blue-600 dark:text-blue-400" />
            </div>
            <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-2">
              No listings found
            </h3>
            <p className="text-slate-600 dark:text-slate-400 mb-6 max-w-md">
              {searchTerm || hasActiveFilters
                ? "Try adjusting your search criteria or filters to find more listings."
                : "Get started by creating your first business listing."}
            </p>
            {!searchTerm && !hasActiveFilters && (
              <Button
                className="gap-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
                asChild
              >
                <Link to="/listings/new">
                  <Plus className="h-4 w-4" />
                  Create Your First Listing
                </Link>
              </Button>
            )}
          </div>
        ) : (
          /* Listings Display */
          <>
            {/* Enhanced Glassmorphic Listings Table */}
            <div className="bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm border-slate-200/50 dark:border-slate-700/50 rounded-lg border mb-8 overflow-hidden shadow-xl shadow-black/5 hover:shadow-2xl transition-all duration-300">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="min-w-48">Business Name</TableHead>
                      <TableHead className="min-w-32">Industry</TableHead>
                      <TableHead className="min-w-24">Status</TableHead>
                      <TableHead className="min-w-24">Price</TableHead>
                      <TableHead className="min-w-32">Location</TableHead>
                      <TableHead className="text-right min-w-48">
                        Actions
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {listings.map((listing) => (
                      <TableRow
                        key={listing.id}
                        className="cursor-pointer hover:bg-slate-50 dark:hover:bg-slate-700/50"
                      >
                        <TableCell>
                          <Link
                            to={`/listings/${listing.id}`}
                            className="font-medium text-slate-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400"
                          >
                            {listing.businessName}
                          </Link>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Building2 className="h-4 w-4 text-slate-400" />
                            <span>{listing.industry}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <StatusBadge status={listing.status} />
                        </TableCell>
                        <TableCell className="font-medium">
                          {formatCurrencyAbbreviated(listing.askingPrice)}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <MapPin className="h-4 w-4 text-slate-400" />
                            <span>{listing.generalLocation}</span>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex gap-2 justify-end">
                            <Button
                              variant="glass"
                              size="sm"
                              className="gap-2 hover:scale-105 hover:shadow-lg transition-all duration-150"
                              onClick={() =>
                                navigate(`/listings/${listing.id}/edit`)
                              }
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="glass"
                              size="sm"
                              className="gap-2 text-red-600 dark:text-red-400 hover:bg-red-50/80 dark:hover:bg-red-900/30 hover:scale-105 hover:shadow-lg transition-all duration-150"
                              onClick={() => handleDeleteClick(null, listing)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>

            {/* Enhanced Glassmorphic Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-center gap-2">
                <Button
                  variant="glass"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="gap-2 hover:scale-105 transition-transform duration-150"
                >
                  Previous
                </Button>

                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const page = i + Math.max(1, currentPage - 2);
                    if (page > totalPages) return null;

                    return (
                      <Button
                        key={page}
                        variant={page === currentPage ? "default" : "glass"}
                        onClick={() => handlePageChange(page)}
                        className={cn(
                          "w-10 h-10 p-0 transition-all duration-150",
                          page === currentPage
                            ? "bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:from-blue-700 hover:to-indigo-700 hover:scale-105"
                            : "hover:scale-105 hover:shadow-lg"
                        )}
                      >
                        {page}
                      </Button>
                    );
                  })}
                </div>

                <Button
                  variant="glass"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="gap-2 hover:scale-105 transition-transform duration-150"
                >
                  Next
                </Button>
              </div>
            )}
          </>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Listing</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{listingToDelete?.businessName}"?
              This action cannot be undone and will permanently remove the
              listing and all associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={deleteMutation.isPending}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                if (listingToDelete?.id) {
                  deleteMutation.mutate(listingToDelete.id);
                }
              }}
              disabled={deleteMutation.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deleteMutation.isPending ? "Deleting..." : "Delete Listing"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default V2_Listings;
